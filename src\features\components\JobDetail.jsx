import jsPDF from "jspdf";
import { useEffect, useRef, useState } from "react";
import { FaDownload, FaPaperPlane, FaPaperclip, FaTrash } from "react-icons/fa";
import * as XLSX from "xlsx";
import closePanelIcon from "../../assets/closePanel.svg";
import completeIcon from "../../assets/complete.svg";
import considerIcon from "../../assets/consider.svg";
import startdateIcon from "../../assets/creationdate.svg";
import deploymentIcon from "../../assets/deployment.svg";
import editIcon from "../../assets/edit.svg";
import enddateIcon from "../../assets/enddate.svg";
import highPriorityIcon from "../../assets/High.svg";
import mediumPriorityIcon from "../../assets/Medium.svg";
import normalPriorityIcon from "../../assets/Normal.svg";
import triangleAlertIcon from "../../assets/triangle-alert.svg";
import waitingIcon from "../../assets/waiting.svg";
import "../../styles/DetailJob.css";
import { showError, showSuccess } from "../../utils/toastUtils";
import ConfirmDeletePopup from "./ConfirmDeletePopup";
// Thêm import API bình luận
import {
  deleteTaskAttachment,
  getTaskAttachments,
  getTaskComments,
  postTaskComment,
  uploadTaskAttachment
} from "../../api/taskManagement";
import JobUpdate from "./JobUpdate";



// Hàm trả về icon trạng thái dựa vào status
const getStatusIcon = (status) => {
  switch (status) {
    case "in_progress":
    case "in-progress":
    case "Đang triển khai":
      return deploymentIcon;
    case "completed":
    case "Hoàn thành":
      return completeIcon;
    case "pending":
    case "waiting":
    case "Đang chờ":
      return waitingIcon;
    case "overdue":
    case "Quá hạn":
      return triangleAlertIcon;
    case "review":
    case "consider":
    case "Xem xét":
      return considerIcon;
    default:
      return waitingIcon;
  }
};
// Hàm trả về text trạng thái dựa vào status
const getStatusText = (status) => {
  switch (status) {
    case "in_progress":
    case "in-progress":
    case "Đang triển khai":
      return "Đang triển khai";
    case "completed":
    case "Hoàn thành":
      return "Hoàn thành";
    case "pending":
    case "waiting":
    case "Đang chờ":
      return "Đang chờ";
    case "overdue":
    case "Quá hạn":
      return "Quá hạn";
    case "review":
    case "consider":
    case "Xem xét":
      return "Xem xét";
    default:
      return status;
  }
};
// Hàm trả về icon mức độ ưu tiên
const getPriorityIcon = (priority) => {
  const p = String(priority).toLowerCase();
  switch (p) {
    case "high":
    case "cao":
      return highPriorityIcon; // đỏ
    case "critical":
    case "urgent":
    case "khẩn cấp":
    case "khan cap":
      return highPriorityIcon; // đỏ
    case "medium":
    case "trung bình":
    case "trung binh":
      return mediumPriorityIcon; // cam
    case "low":
    case "thấp":
    case "thap":
      return normalPriorityIcon; // xanh lá
    default:
      return normalPriorityIcon;
  }
};
// Hàm trả về text mức độ ưu tiên
const getPriorityText = (priority) => {
  if (!priority) return "Chưa xác định";
  const p = String(priority).toLowerCase();
  switch (p) {
    case "critical":
    case "urgent":
    case "khẩn cấp":
    case "khan cap":
      return "Khẩn cấp";
    case "high":
    case "cao":
      return "Cao";
    case "medium":
    case "trung bình":
    case "trung binh":
      return "Trung bình";
    case "low":
    case "thấp":
    case "thap":
      return "Thấp";
    default:
      if (["Cao", "Trung bình", "Thấp", "Khẩn cấp"].includes(priority)) return priority;
      return "Thấp";
  }
};

// Helper format date
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  
  // If it's already in DD/MM/YYYY format, return as is
  if (typeof dateStr === 'string' && /^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
    return dateStr;
  }
  
  const d = new Date(dateStr);
  if (isNaN(d)) return dateStr;
  
  // Format to DD/MM/YYYY
  const day = d.getDate();
  const month = d.getMonth() + 1;
  const year = d.getFullYear();
  
  return `${day}/${month}/${year}`;
};
// Helper format mã
const formatCode = (id, prefix = '') => {
  if (!id) return '';
  const str = String(id);
  return prefix + str.slice(-6);
};

// Helper lấy avatar mặc định nếu không có
const getUserAvatar = (user) => {
  const defaultAvatar = 'https://randomuser.me/api/portraits/men/1.jpg';
  if (!user) return defaultAvatar;
  // Kiểm tra avatar trực tiếp
  if (user.avatar && user.avatar.trim() !== '' && !user.avatar.includes('ui-avatars.com/api/?name=?')) {
    return user.avatar;
  }
  // Kiểm tra avatar trong user.user
  if (user.user && user.user.avatar && user.user.avatar.trim() !== '' && !user.user.avatar.includes('ui-avatars.com/api/?name=?')) {
    return user.user.avatar;
  }
  return defaultAvatar;
};



// Component hiển thị chi tiết công việc
const DetailJob = ({
  task,
  onClose,
  onTaskUpdate,
  hideActivityAndComment = false,
  hideExport = false,
}) => {
  // Quản lý trạng thái hiển thị panel chi tiết
  const [visible, setVisible] = useState(false);
  // Quản lý nội dung bình luận
  const [commentText, setCommentText] = useState("");
  // Quản lý danh sách hoạt động
  const [activities, setActivities] = useState([]);
  // Quản lý danh sách tệp đính kèm
  const [attachments, setAttachments] = useState([]);
  const [loadingAttachments, setLoadingAttachments] = useState(false);
  // Quản lý popup xác nhận xóa
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [fileToDelete, setFileToDelete] = useState(null);
  // Quản lý chế độ chỉnh sửa
  const [isEditing, setIsEditing] = useState(false);
  // Tham chiếu đến input file
  const fileInputRef = useRef(null);
  // Tham chiếu đến khu vực hoạt động
  const activitySectionRef = useRef(null);
  // Tham chiếu đến khu vực tệp đính kèm
  const attachmentSectionRef = useRef(null);
  // Tham chiếu đến nội dung panel
  const contentRef = useRef(null);
  // State mới cho bình luận
  const [comments, setComments] = useState([]);
  const [loadingComments, setLoadingComments] = useState(false);
  // Ref để track việc upload temp files
  const tempFilesProcessedRef = useRef(false);

  useEffect(() => {
    if (task) {
      setTimeout(() => setVisible(true), 10);
      // Khởi tạo danh sách hoạt động từ dữ liệu task
      setActivities(task.activities || []);
      // Reset ref khi task thay đổi
      tempFilesProcessedRef.current = false;

      // Đảm bảo scrollbar luôn ở trên cùng khi mở lại panel
      if (contentRef.current) {
        contentRef.current.scrollTop = 0;
      }
      // Lấy bình luận và attachments từ API
      fetchComments();
      fetchAttachments();
    } else {
      setVisible(false);
    }
  }, [task]);

  // Hàm lấy bình luận từ API
  const fetchComments = async () => {
    if (!task) return;
    setLoadingComments(true);
    try {
      const projectId = task.projectId;
      const taskId = task.id || task._id;
  
      if (!projectId || !taskId) {
        setComments([]);
        setLoadingComments(false);
        return;
      }
      const res = await getTaskComments(projectId, taskId);
      setComments(res.data || res || []);
    } catch (e) {
      setComments([]);
    }
    setLoadingComments(false);
  };

  // Hàm lấy attachments từ API
  const fetchAttachments = async () => {
    if (!task) return;
    setLoadingAttachments(true);

    try {
      const projectId = task.projectId;
      const taskId = task.id || task._id;

      if (!projectId || !taskId) {
        // Nếu không có projectId hoặc taskId, chỉ hiển thị non-temp attachments
        if (task.attachments && task.attachments.length > 0) {
          const nonTempAttachments = task.attachments.filter(att => !att.isTemp);
          setAttachments(nonTempAttachments);
        } else {
          setAttachments([]);
        }
        setLoadingAttachments(false);
        return;
      }

      // Kiểm tra xem có temp files cần upload không
      const tempFiles = task.attachments ? task.attachments.filter(att => att.isTemp && att.file) : [];

      if (tempFiles.length > 0) {
        // Nếu có temp files, upload chúng trước
        console.log(`Uploading ${tempFiles.length} temp files...`);

        for (const tempFile of tempFiles) {
          try {
            // Kiểm tra kích thước file (10MB = 10485760 bytes)
            if (tempFile.file.size > 10485760) {
              alert(`File "${tempFile.name}" quá lớn (${Math.round(tempFile.file.size/1024/1024)}MB). Kích thước tối đa là 10MB.`);
              continue;
            }

            await uploadTaskAttachment(projectId, taskId, tempFile.file);
            console.log(`Uploaded: ${tempFile.name}`);
          } catch (uploadError) {
            console.error(`Upload error for ${tempFile.name}:`, uploadError);
            if (uploadError.message.includes("File size too large")) {
              alert(`File "${tempFile.name}" quá lớn. Kích thước tối đa là 10MB.`);
            } else {
              alert(`Lỗi upload file "${tempFile.name}": ${uploadError.message}`);
            }
          }
        }

        // Xóa temp files khỏi task object sau khi upload xong
        if (task.attachments) {
          task.attachments = task.attachments.filter(att => !att.isTemp);
        }
        console.log("All temp files uploaded, fetching from API...");
      }

      // Luôn luôn fetch từ API để lấy danh sách attachments mới nhất
      const res = await getTaskAttachments(projectId, taskId);
      const attachments = res.data || res || [];
      console.log("API response attachments:", attachments);

      // Transform backend data to frontend format và hiển thị
      const finalAttachments = attachments.map((att) => ({
        _id: att._id,
        name: att.fileName,
        size: "N/A", // Backend không trả về size
        type: att.fileName.split(".").pop().toLowerCase(),
        date: new Date(att.uploadedAt).toLocaleDateString("vi-VN"),
        url: att.fileUrl,
        uploadedBy: att.uploadedBy,
      }));

      console.log("Final attachments to display:", finalAttachments);
      setAttachments(finalAttachments);
    } catch (e) {
      console.error("Error in fetchAttachments:", e);
      // Nếu có lỗi API, chỉ hiển thị non-temp attachments
      if (task.attachments && task.attachments.length > 0) {
        const nonTempAttachments = task.attachments.filter(att => !att.isTemp);
        setAttachments(nonTempAttachments);
      } else {
        setAttachments([]);
      }
    }
    setLoadingAttachments(false);
  };

  // Hàm khởi tạo chế độ chỉnh sửa
  const handleEditClick = () => {
    setIsEditing(true);
  };

  // Hàm hủy chỉnh sửa
  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  // Hàm xử lý khi JobUpdate component lưu thành công
  const handleJobUpdateSave = (updatedTask) => {
    setIsEditing(false);

    // Update the current task display immediately
    if (updatedTask && task) {
      // Update the task object that's being displayed
      Object.assign(task, {
        name: updatedTask.name,
        description: updatedTask.description,
        startDate: updatedTask.startDate,
        endDate: updatedTask.endDate,
        dueDate: updatedTask.dueDate,
        priority: updatedTask.priority,
        status: updatedTask.status
      });

      // Call parent callback to update the task list immediately
      if (onTaskUpdate) {
        onTaskUpdate(updatedTask);
      }
    }

    console.log('Task updated successfully:', updatedTask);

    // The task list will be automatically refreshed by the event listeners
    // in other components that listen to "projectTasksUpdated" event
  };







  if (!task) return null;

  // Đóng panel khi click ra ngoài overlay
  const handleOverlayClick = (e) => {
    if (e.target.classList.contains("detail-job-overlay")) {
      handleClose();
    }
  };

  // Đóng panel với hiệu ứng
  const handleClose = () => {
    setVisible(false);
    setTimeout(() => onClose && onClose(), 250);
  };

  // Xử lý gửi bình luận mới
  const handleSendComment = async () => {
    if (commentText.trim()) {
      const projectId = task.projectId;
      const taskId = task.id || task._id;

      try {
        await postTaskComment(projectId, taskId, commentText.trim());
        setCommentText("");
        fetchComments(); // reload lại bình luận
      } catch (e) {
        alert("Gửi bình luận thất bại");
      }
    }
  };

  // Xử lý nhấn Enter để gửi bình luận
  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      handleSendComment();
    }
  };

  // Xử lý khi nhấn nút đính kèm file
  const handleAttachFile = () => {
    fileInputRef.current.click();
  };

  // Xử lý khi chọn file đính kèm
  const handleFileSelected = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      const projectId = task.projectId;
      const taskId = task.id || task._id;

      // Upload từng file
      for (const file of files) {
        try {
          await uploadTaskAttachment(projectId, taskId, file);

          // Thêm hoạt động khi upload file thành công
          const newActivity = {
            user: "Bạn",
            content: `đã đính kèm tệp: ${file.name}`,
            timestamp: new Date().toLocaleString("vi-VN"),
          };
          setActivities((prev) => [newActivity, ...prev]);
        } catch (error) {
          alert(`Lỗi upload file ${file.name}: ${error.message}`);
        }
      }

      // Reload attachments sau khi upload
      fetchAttachments();

      // Xóa nội dung comment
      setCommentText("");

      // Cuộn đến khu vực tệp đính kèm
      if (attachmentSectionRef.current) {
        attachmentSectionRef.current.scrollIntoView({ behavior: "smooth" });
      }

      // Reset input file
      e.target.value = "";
    }
  };

  // Xử lý tải file đính kèm
  const handleDownloadFile = (file) => {
    if (file.url) {
      // Mở file trong tab mới (Cloudinary URL)
      window.open(file.url, "_blank");
    } else {
      alert(`Không thể tải xuống: ${file.name}`);
    }
  };

  // Xử lý xóa file đính kèm
  const handleDeleteFile = (fileIndex, e) => {
    e.stopPropagation(); // Ngăn không cho trigger download khi click vào nút xóa

    const fileData = attachments[fileIndex];
    setFileToDelete({ file: fileData, index: fileIndex });
    setShowDeleteConfirm(true);
  };

  // Xác nhận xóa file
  const handleConfirmDelete = async () => {
    if (fileToDelete) {
      try {
        const projectId = task.projectId;
        const taskId = task.id || task._id;
        const attachmentId = fileToDelete.file._id;

        // Gọi API xóa file
        await deleteTaskAttachment(projectId, taskId, attachmentId);

        // Thêm hoạt động khi xóa file thành công
        const newActivity = {
          user: "Bạn",
          content: `đã xóa tệp đính kèm: ${fileToDelete.file.name}`,
          timestamp: new Date().toLocaleString("vi-VN"),
        };
        setActivities((prev) => [newActivity, ...prev]);

        // Reload attachments
        fetchAttachments();
      } catch (error) {
        alert(`Lỗi xóa file: ${error.message}`);
      }
    }

    // Đóng popup và reset state
    setShowDeleteConfirm(false);
    setFileToDelete(null);
  };

  // Hủy xóa file
  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
    setFileToDelete(null);
  };

  // Export PDF
  const handleExportPDF = () => {
    const doc = new jsPDF();
    doc.setFontSize(14);
    doc.text(`Chi tiết dự án: ${task.name}`, 10, 10);
    doc.setFontSize(11);
    doc.text(`ID dự án: ${task.code || ""}`, 10, 20);
    doc.text(`Trạng thái: ${getStatusText(task.status)}`, 10, 30);
    doc.text(`Người tạo: ${task.creator.name}`, 10, 40);
    doc.text(
      `Thành viên: ${task.assignee.map((a) => a.name).join(", ")}`,
      10,
      50
    );
    doc.text(`Mức độ ưu tiên: ${getPriorityText(task.priority)}`, 10, 60);
    doc.text(`Tiến độ: ${task.progress ? task.progress + "%" : ""}`, 10, 70);
    doc.text(`Ngày tạo: ${task.startDate}`, 10, 80);
    doc.text(`Ngày kết thúc: ${task.dueDate}`, 10, 90);
    doc.text(`Mô tả: ${task.description || ""}`, 10, 100);
    if (attachments.length > 0) {
      doc.text("Tệp đính kèm:", 10, 110);
      attachments.forEach((file, idx) => {
        doc.text(`- ${file.name} (${file.date})`, 15, 120 + idx * 8);
      });
    }
    doc.save(`${task.name || "project"}.pdf`);
  };

  // Export Excel
  const handleExportExcel = () => {
    try {
      // Lấy thông tin người tạo an toàn
      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
      const user = currentUser.user || currentUser;
      const creatorName = user.fullName || user.name || 'Người dùng hiện tại';
      
      // Lấy thông tin thành viên an toàn
      const memberNames = (task.assignedTo || task.assignee || []).map((a) => 
        a.fullName || a.name || 'Thành viên'
      ).join(", ");
      
      const wsData = [
        ["Chi tiết dự án", task.name || 'Không có tên'],
        ["ID dự án", task.projectCode || task.code || ""],
        ["Trạng thái", getStatusText(task.status)],
        ["Người tạo", creatorName],
        ["Thành viên", memberNames || "Không có thành viên"],
        ["Mức độ ưu tiên", getPriorityText(task.priority)],
        ["Tiến độ", task.progress ? task.progress + "%" : "0%"],
        ["Ngày tạo", task.startDate || ""],
        ["Ngày kết thúc", task.endDate || task.dueDate || ""],
        ["Mô tả", task.description || ""],
        [
          "Tệp đính kèm",
          (attachments || []).map((f) => f.name + " (" + f.date + ")").join("; ") || "Không có tệp đính kèm",
        ],
      ];
      const ws = XLSX.utils.aoa_to_sheet(wsData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "ChiTietDuAn");
      XLSX.writeFile(wb, `${task.name || "project"}.xlsx`);
      
      // Hiển thị thông báo thành công
      showSuccess("Xuất Excel thành công!");
    } catch (error) {
      // Hiển thị thông báo lỗi
      showError("Lỗi khi xuất Excel: " + error.message);
    }
  };

  return (
    <>
      {/* Component chỉnh sửa công việc - render riêng biệt */}
      {isEditing && (
        <JobUpdate
          task={task}
          onCancel={handleCancelEdit}
          onSave={handleJobUpdateSave}
          disableDepartmentFilter={false}
        />
      )}

      {/* Component hiển thị chi tiết công việc - chỉ hiển thị khi không chỉnh sửa */}
      {!isEditing && (
        <div
          className="detail-job-overlay"
          onClick={handleOverlayClick}
          style={{
            position: "fixed",
            inset: 0,
            background: "rgba(0,0,0,0.08)",
            zIndex: 999,
          }}
        >
        <div className={`detail-job-container${visible ? " show" : ""}`}>
          <div className="detail-job-header-actions">
            <button
              className="detail-job-edit-btn"
              onClick={handleEditClick}
              title="Chỉnh sửa"
            >
              <img
                src={editIcon}
                alt="Chỉnh sửa"
                style={{ width: 20, height: 20, opacity: 0.6 }}
              />
            </button>
            <button
              className="detail-job-close-btn"
              onClick={handleClose}
              title="Đóng"
            >
              <img
                src={closePanelIcon}
                alt="Đóng"
                style={{ width: 20, height: 20 }}
              />
            </button>
          </div>

          {/* Header cố định */}
          <div className="detail-job-header">
            <h3>{task.name}</h3>
          </div>

        <div className="detail-job-content" ref={contentRef} style={{ display: isEditing ? 'none' : 'block' }}>
          <div className="detail-row">
            <span className="detailjob-label">Trạng thái</span>
            <span className="detailjob-value">
              <img
                src={getStatusIcon(task.status)}
                alt=""
                style={{ width: 18, marginRight: 4 }}
              />
              {getStatusText(task.status)}
            </span>
          </div>
          <div className="detail-row">
            <span className="detailjob-label">Người tạo</span>
            <span className="detailjob-value">
              {(() => {
                // Luôn lấy người đang đăng nhập làm người tạo
                const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
                const user = currentUser.user || currentUser;
                const creatorName = user.fullName || user.name || 'Người dùng hiện tại';
                const creatorAvatar = user.avatar || 'https://ui-avatars.com/api/?name=?';
                
                return (
                  <div style={{ position: 'relative', display: 'inline-block', verticalAlign: 'middle' }}>
                    <img
                      src={creatorAvatar}
                      alt={creatorName}
                      className="detail-avatar"
                      title={creatorName}
                      onError={e => { e.target.src = "https://randomuser.me/api/portraits/men/1.jpg"; }}
                      onMouseEnter={(e) => {
                        const tooltip = document.createElement('div');
                        tooltip.textContent = creatorName;
                        tooltip.style.cssText = `
                          position: absolute;
                          background: rgba(0,0,0,0.8);
                          color: white;
                          padding: 4px 8px;
                          border-radius: 4px;
                          font-size: 12px;
                          z-index: 1000;
                          white-space: nowrap;
                          pointer-events: none;
                          top: -30px;
                          left: 50%;
                          transform: translateX(-50%);
                        `;
                        tooltip.id = 'creator-tooltip';
                        e.target.parentNode.appendChild(tooltip);
                      }}
                      onMouseLeave={() => {
                        const tooltip = document.getElementById('creator-tooltip');
                        if (tooltip) tooltip.remove();
                      }}
                    />
                  </div>
                );
              })()}
            </span>
          </div>
          <div className="detail-row">
            <span className="detailjob-label">Thành viên</span>
            <span className="detailjob-value">
              <div className="detail-member-list">
                {(task.assignee && Array.isArray(task.assignee) && task.assignee.length > 0) ? (
                  task.assignee.map((mem, idx) => (
                    <img
                      key={idx}
                      src={getUserAvatar(mem)}
                      alt={mem.fullName || mem.name || 'Thành viên'}
                      className="detail-avatar"
                      title={mem.fullName || mem.name || 'Thành viên'}
                      onError={e => { e.target.src = "https://randomuser.me/api/portraits/men/1.jpg"; }}
                    />
                  ))
                ) : (task.assignedTo && Array.isArray(task.assignedTo) && task.assignedTo.length > 0) ? (
                  task.assignedTo.map((mem, idx) => (
                    <img
                      key={idx}
                      src={getUserAvatar(mem)}
                      alt={mem.fullName || mem.name || 'Thành viên'}
                      className="detail-avatar"
                      title={mem.fullName || mem.name || 'Thành viên'}
                      onError={e => { e.target.src = "https://randomuser.me/api/portraits/men/1.jpg"; }}
                    />
                  ))
                ) : (task.assignedToId && (task.assignedToId.fullName || task.assignedToId.name)) ? (
                  <img
                    src={getUserAvatar(task.assignedToId)}
                    alt={task.assignedToId.fullName || task.assignedToId.name || 'Thành viên'}
                    className="detail-avatar"
                    title={task.assignedToId.fullName || task.assignedToId.name || 'Thành viên'}
                    onError={e => { e.target.src = "https://randomuser.me/api/portraits/men/1.jpg"; }}
                  />
                ) : (
                  <span style={{ color: '#888', fontStyle: 'italic' }}>Chưa phân công</span>
                )}
              </div>
            </span>
          </div>
          <div className="detail-row">
            <span className="detailjob-label">Mức độ ưu tiên</span>
            <span className="detailjob-value">
              {(() => {
                const priorityIcon = getPriorityIcon(task.priority);
                const priorityText = getPriorityText(task.priority);
                return (
                  <>
                    <img
                      src={priorityIcon}
                      alt="priority"
                      style={{ width: 18, marginRight: 4 }}
                      onError={(e) => {
                        // Fallback cho từng mức độ ưu tiên
                        const p = String(task.priority).toLowerCase();
                        if (p === 'medium' || p === 'trung bình' || p === 'trung binh') {
                          e.target.style.filter = 'hue-rotate(30deg) saturate(1.5)'; // Cam
                        } else if (p === 'high' || p === 'cao' || p === 'critical' || p === 'urgent') {
                          e.target.style.filter = 'hue-rotate(0deg) saturate(2)'; // Đỏ
                        } else {
                          e.target.style.filter = 'hue-rotate(120deg) saturate(1.2)'; // Xanh lá
                        }
                      }}
                    />
                    {priorityText}
                  </>
                );
              })()}
            </span>
          </div>
          <div className="detail-row">
            <span className="detailjob-label">Hoàn thành</span>
            <span className="detailjob-value detail-progress-value">
              <span>{task.progress ? task.progress + "%" : "0%"}</span>
              <div className="detail-progress-bar">
                <div
                  className="detail-progress-bar-fill"
                  style={{ width: `${task.progress || 0}%` }}
                ></div>
              </div>
            </span>
          </div>
          {/* Mã công việc chỉ hiển thị nếu task.id khác projectId (tức là task thực sự) */}
          {task.id && task.id !== task.projectId && (
            <div className="detail-row">
              <span className="detailjob-label">Mã công việc</span>
              <span className="detailjob-value">{formatCode(task.id || task._id, 'TSK-')}</span>
            </div>
          )}
          <div className="detail-row">
            <span className="detailjob-label">Mã dự án</span>
            <span className="detailjob-value">{task.projectId?.projectCode || task.projectCode || 'PRJ-N/A'}</span>
          </div>
          <div className="detail-row">
            <span className="detailjob-label">Ngày tạo</span>
            <span className="detailjob-value">
              <img
                src={startdateIcon}
                alt="Ngày tạo"
                style={{ width: 20,
                  marginRight: 6,
                  marginLeft: -2,
                  verticalAlign: "middle", }}
              />
              {formatDate(task.startDate)}
            </span>
          </div>
          <div className="detail-row">
            <span className="detailjob-label">Ngày kết thúc</span>
            <span className="detailjob-value">
              <img
                src={enddateIcon}
                alt="Ngày kết thúc"
                style={{
                  width: 20,
                  marginRight: 6,
                  marginLeft: -2,
                  verticalAlign: "middle",
                }}
              />
              {formatDate(task.endDate || task.dueDate)}
            </span>
          </div>
          <div className="detail-row">
            <span className="detailjob-label">Mô tả</span>
            <span className="detailjob-value">
              {task.description || "Không có mô tả"}
            </span>
          </div>
          <div
            className="detail-row attachment-section"
            ref={attachmentSectionRef}
          >
            <span className="detailjob-label">Tệp đính kèm</span>
            <span className="detailjob-value">
              {attachments.length > 0 ? (
                attachments.map((file, idx) => (
                  <div
                    key={idx}
                    className="detail-attachment-item"
                    onClick={() => handleDownloadFile(file)}
                  >
                    <span className="detail-attachment-icon">
                      {file.type === "xls" || file.type === "xlsx"
                        ? "📗"
                        : "📄"}
                    </span>
                    <span className="detail-attachment-name">{file.name}</span>
                    <span className="detail-attachment-date">{file.date}</span>
                    <span className="detail-attachment-actions">
                      <span
                        className="detail-attachment-download"
                        title="Tải xuống"
                      >
                        <FaDownload size={12} />
                      </span>
                      <span
                        className="detail-attachment-delete"
                        title="Xóa tệp"
                        onClick={(e) => handleDeleteFile(idx, e)}
                      >
                        <FaTrash size={11} />
                      </span>
                    </span>
                  </div>
                ))
              ) : (
                <span style={{ color: "#888", fontStyle: "italic" }}>
                  Chưa có tệp đính kèm
                </span>
              )}
            </span>
          </div>
          {/* Ẩn nút xuất file nếu hideExport = true */}
          {!hideExport && (
            <div className="detail-export-btns">
              {/* <button onClick={handleExportPDF} className="detail-export-btn detail-export-btn-pdf">
                <span role="img" aria-label="pdf">📄</span> Xuất PDF
              </button> */}
              <button
                onClick={handleExportExcel}
                className="detail-export-btn detail-export-btn-excel"
              >
                <span role="img" aria-label="excel">
                  📗
                </span>{" "}
                Xuất Exel
              </button>
            </div>
          )}
          {/* Ẩn hoạt động nếu hideActivityAndComment là true */}
          {!hideActivityAndComment && (
            <div
              className="detail-row detail-row-activity"
              ref={activitySectionRef}
            >
              <span className="detailjob-label">Bình luận</span>
              <span className="detailjob-value">
                {loadingComments ? (
                  <div>Đang tải...</div>
                ) : (
                  <div className="detail-activity-list">
                    {comments.length > 0 ? (
                      comments.map((c, idx) => (
                        <div key={idx} className="detail-activity-item">
                          <img
                            src={c.user?.avatar || ""}
                            alt={c.user?.name || ""}
                            className="detail-activity-avatar"
                          />
                          <div className="detail-activity-content">
                            <span className="detail-activity-name">
                              {c.user?.name || c.user?.fullName || "Ẩn danh"}
                            </span>
                            <span className="detail-activity-text">
                              {c.content}
                            </span>
                            <span className="detail-activity-time">
                              {c.createdAt
                                ? new Date(c.createdAt).toLocaleString("vi-VN")
                                : ""}
                            </span>
                          </div>
                        </div>
                      ))
                    ) : (
                      <span style={{ color: "#888", fontStyle: "italic" }}>
                        Chưa có bình luận
                      </span>
                    )}
                  </div>
                )}
              </span>
            </div>
          )}
        </div>
        {/* Ẩn bình luận nếu hideActivityAndComment là true hoặc đang ở chế độ chỉnh sửa */}
        {!hideActivityAndComment && !isEditing && (
          <div className="detail-comment-footer">
            <div className="detail-row detail-row-comment">
              <div
                className="detail-comment-box-wrapper"
                style={{ paddingRight: 16 }}
              >
                <div className="detailjob-comment-box">
                  <input
                    className="detail-comment-input"
                    placeholder="Viết bình luận cho bạn"
                    value={commentText}
                    onChange={(e) => setCommentText(e.target.value)}
                    onKeyPress={handleKeyPress}
                  />
                  <div className="detail-comment-actions">
                    <input
                      type="file"
                      ref={fileInputRef}
                      style={{ display: "none" }}
                      multiple
                      onChange={handleFileSelected}
                    />
                    <button
                      className="detail-comment-attach"
                      title="Đính kèm file"
                      onClick={handleAttachFile}
                    >
                      <FaPaperclip />
                    </button>
                    <button
                      className="detail-comment-btn"
                      title="Gửi bình luận"
                      onClick={handleSendComment}
                      disabled={!commentText.trim()}
                    >
                      <FaPaperPlane />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Popup xác nhận xóa */}
        <ConfirmDeletePopup
          isOpen={showDeleteConfirm}
          onConfirm={handleConfirmDelete}
          onCancel={handleCancelDelete}
          fileName={fileToDelete?.file?.name || ""}
        />

        </div>
        </div>
      )}
    </>
  );
};

export default DetailJob;
