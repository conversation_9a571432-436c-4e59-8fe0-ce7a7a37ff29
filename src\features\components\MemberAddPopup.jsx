import { useState } from "react";
import "../../styles/MemberAddPopup.css";

const MemberAddPopup = ({ isOpen, onClose, onAddMember, existingMembers = [], users = [], loadingUsers = false, departmentId, disableDepartmentFilter = false, leaderId, filterByDepartment = false }) => {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Lọc users theo các điều kiện:
  // 1. Lọc theo departmentId (nếu có và không disable và filterByDepartment = true)
  // 2. Chỉ lấy users có role 'staff' (nếu filterByDepartment = true - dành cho project creation)
  // 3. Loại trừ leader đã chọn
  const filteredUsers = users.filter(user => {
    // Lọc theo department nếu cần (cho project creation)
    if (filterByDepartment && !disableDepartmentFilter && departmentId && user.departmentId !== departmentId) {
      return false;
    }
    
    // Chỉ lấy staff (cho project creation)
    if (filterByDepartment) {
    const userRole = (user.role || "").toLowerCase();
    if (userRole !== 'staff') {
      return false;
      }
    }
    
    // Loại trừ leader đã chọn
    if (leaderId && user.id === leaderId) {
      return false;
    }
    
    return true;
  });

  // Lọc gợi ý dựa trên email đã nhập
  const handleEmailChange = (value) => {
    setEmail(value);
    setError("");
    const inputValue = value.trim().toLowerCase();
    if (inputValue.length > 0 && filteredUsers.length > 0) {
      const filteredSuggestions = filteredUsers.filter(user =>
        (user.email.toLowerCase().includes(inputValue) ||
         user.name.toLowerCase().includes(inputValue)) &&
        !existingMembers.some(member => member.email.toLowerCase() === user.email.toLowerCase())
      );
      setSuggestions(filteredSuggestions);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Chọn thành viên từ gợi ý
  const handleSelectSuggestion = (user) => {
    setEmail(user.email);
    setSuggestions([]);
    setShowSuggestions(false);
  };

  // Xử lý thêm thành viên
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setError("Vui lòng nhập email thành viên");
      return;
    }

    // Kiểm tra email có tồn tại trong hệ thống không
    const user = users.find(u => u.email.toLowerCase() === email.toLowerCase());
    if (!user) {
      setError("Email không tồn tại trong hệ thống");
      return;
    }

    // Kiểm tra thành viên đã được thêm chưa
    if (existingMembers.some(member => member.email === email)) {
      setError("Thành viên đã được thêm vào dự án");
      return;
    }

    // Thêm thành viên
    onAddMember(user);
    
    // Reset form
    setEmail("");
    setError("");
    setSuggestions([]);
    setShowSuggestions(false);
    onClose();
  };

  // Đóng popup
  const handleClose = () => {
    setEmail("");
    setError("");
    setSuggestions([]);
    setShowSuggestions(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="member-add-overlay">
      <div className="member-add-popup">
        <div className="member-add-header">
          <h3>Thêm thành viên vào dự án</h3>
          <button className="member-add-close-btn" onClick={handleClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="member-add-form">
          <div className="member-add-form-group">
            <label>Email thành viên</label>
            <div className="member-add-input-container">
              <input
                type="text"
                placeholder={loadingUsers ? "Đang tải danh sách thành viên..." : "Nhập email thành viên..."}
                value={email}
                onChange={(e) => handleEmailChange(e.target.value)}
                className={`member-add-input ${error ? 'error' : ''}`}
                autoComplete="off"
                disabled={loadingUsers || (!disableDepartmentFilter && !departmentId)}
              />
              
              {/* Thông báo nếu chưa chọn phòng ban */}
              {!disableDepartmentFilter && !departmentId && !loadingUsers && (
                <div className="member-add-error">Vui lòng chọn phòng ban trước khi thêm thành viên</div>
              )}
              {/* Danh sách gợi ý */}
              {showSuggestions && suggestions.length > 0 && !loadingUsers && (disableDepartmentFilter || departmentId) && (
                <div className="member-add-suggestions">
                  {suggestions.map((user, index) => (
                    <div
                      key={index}
                      className="member-add-suggestion-item"
                      onClick={() => handleSelectSuggestion(user)}
                    >
                      <img src={user.avatar} alt={user.name} className="suggestion-avatar" />
                      <div className="suggestion-info">
                        <div className="suggestion-name">{user.name}</div>
                        <div className="suggestion-email">{user.email}</div>
                        <div className="suggestion-department">{user.department} - {user.role}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {error && <div className="member-add-error">{error}</div>}
          </div>

          <div className="member-add-actions">
            <button type="button" className="member-add-cancel-btn" onClick={handleClose}>
              Hủy
            </button>
            <button type="submit" className="member-add-submit-btn" disabled={loadingUsers}>
              Thêm thành viên
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MemberAddPopup;
